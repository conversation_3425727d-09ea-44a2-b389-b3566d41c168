<?php

namespace App\Admin\Actions\Grid\Dispute;

use App\Jobs\ChargebackBlackListHandle;
use App\Models\Chargeback;
use App\Models\ChargebackHistory;
use App\Models\Order as OrderModel;
use App\Models\DirectoryCurrency;
use App\Models\DirectoryChargebackCode;
use App\Models\OrderRelation;
use App\Models\OrderSettlement;
use App\Models\PaymentOrder;
use App\Events\ChargebackSettle;
use App\Jobs\ChargebackPenalty;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use App\Services\ChargebackService;
use App\Services\OrderRelationService;

class ApplyChargeback extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        $orderId = $this->payload['order_id'];

        if (empty($orderId) || !intval($orderId)) {
            return $this->response()->error('非法请求!');
        }

        if (
            empty($input['chargeback_currency']) ||
            empty($input['chargeback_at']) ||
            empty($input['chargeback_deadline']) ||
            empty($input['chargeback_code']) ||
            empty($input['arn']) ||
            !isset($input['chargeback_amount'])
        ) {
            return $this->response()->error('参数错误, 请仔细核对再提交');
        }

		if (ChargebackService::checkImport($orderId) || Chargeback::where('order_id', $orderId)->exists()){
			return $this->response()->error("{$orderId}订单已拒付");
		}

        $input['chargeback_amount']  = floatval($input['chargeback_amount']);
        $input['chargeback_remarks'] = isset($input['chargeback_remarks']) ? $input['chargeback_remarks'] : '';
        $input['chargeback_remarks'] = preg_replace('/<script[\s\S]*?<\/script>/i', '', $input['chargeback_remarks']);

        // 拒付工单与拒付录入记录写入
        $chargebackTmp = ChargebackService::chargebackSave($orderId, $input);

        if (!$chargebackTmp['success']) {
            return $this->response()->error($chargebackTmp['errMsg']);
        }

        $chargeback        = $chargebackTmp['chargeback'];
        $chargebackId      = $chargebackTmp['chargeback_id'];
        $chargebackHistory = $chargebackTmp['chargebackHistory'];

        try {
            // 订单关系更新拒付状态
            if (!$chargeback->is_appeal_success || $chargeback->status !== OrderRelation::IS_CHARGEBACK_COMPLETIONS) {
                $orderRelations = OrderRelation::where('order_id', $orderId)->first();

                if (!$orderRelations) {
                    OrderRelationService::orderRelationCreate($orderId);
                    $orderRelations = OrderRelation::where('order_id', $orderId)->first();
                }

                $orderRelations->is_chargeback = OrderRelation::IS_CHARGEBACK_COMPLETIONS;

                if (!$orderRelations->save()) {
                    throw new \Exception('更新订单关系失败');
                }
            }

            // 拒付结算
            $orderSettlement                          = new OrderSettlement();
            $orderSettlement->order_id                = $chargebackId;
            $orderSettlement->parent_order_id         = $orderId;
            $orderSettlement->currency                = $input['chargeback_currency'];
            $orderSettlement->amount                  = $input['chargeback_amount'];
            $orderSettlement->payment_currency        = $input['chargeback_currency'];
            $orderSettlement->payment_amount          = $input['chargeback_amount'];
            $orderSettlement->payment_settle_currency = $input['chargeback_currency'];
            $orderSettlement->payment_settle_amount   = $input['chargeback_amount'];
            $orderSettlement->completed_at            = $chargeback->created_at;

            // 拒付结算事件
            $this->afterChargebackSettle($orderSettlement);

            // 事件执行后查询订单结算表有无相关记录
            if (!OrderSettlement::where('order_id', $orderSettlement->order_id)->exists()) {
                throw new \Exception('订单结算信息保存失败');
            }
        } catch (\Exception $exception) {
            // 申请失败时 失败原因添加录入记录表
            $chargebackHistory = ChargebackService::chargebackHistorySave($orderId, $chargebackId, $input, true, $exception->getMessage());
            $chargebackHistory->save();

            return $this->response()->error('操作失败,原因是:' . $exception->getMessage());
        }

		$chargebackBlackList[$orderId] = $chargebackHistory->type;

		// 拒付工单保存完成，卡号、邮箱、账单人姓名录入黑名单
		dispatch(new ChargebackBlackListHandle($chargebackBlackList));

        //拒付工单完成，收取拒付罚金*
        dispatch(new ChargebackPenalty(['chargeback' => $chargeback->toArray(), 'chargeback_id' => $chargebackId]));
        return $this->response()->success('操作成功')->redirect('/chargeback');
    }

    public function form()
    {
        $this->disableSubmitButton();
        $this->disableResetButton();

        $orderId            = $this->payload['order_id'];
        $orderResult        = OrderModel::with('card')->find($orderId);
        $paymentOrderResult = PaymentOrder::firstWhere('order_id', $orderId);

        if (!$orderResult || empty($orderResult->card) || !$paymentOrderResult) {
            return ['error' => true, 'msg' => '该订单号的订单数据缺失'];
        }

        $chargebackResult        = Chargeback::where('order_id', $orderId)->where('use_flag', 0)->first();
        $chargebackHistoryResult = ChargebackHistory::where('order_id', $orderId)->orderBy('created_at', 'desc')->first();

        if (!$chargebackResult) {
            $chargebackResult                      = new Chargeback();
            $chargebackResult->status              = '';
            $chargebackResult->chargeback_status   = Chargeback::CHARGEBACK_STATUS_ONE_TIME_REFUSAL;
            $chargebackResult->chargeback_deadline = date('Y-m-d H:i:s', time());
            $chargebackResult->remarks             = '';
        }

        if (!$chargebackHistoryResult) {
            $chargebackHistoryResult                  = new ChargebackHistory();
            $chargebackHistoryResult->currency        = $orderResult->currency;
            $chargebackHistoryResult->amount          = $orderResult->amount;
            $chargebackHistoryResult->type            = ChargebackHistory::TYPE_FRAUD;
            $chargebackHistoryResult->chargeback_at   = date('Y-m-d H:i:s', time());
            $chargebackHistoryResult->chargeback_code = '';
            $chargebackHistoryResult->arn             = $paymentOrderResult->payment_order_id;
        }

        $currencyList       = DirectoryCurrency::select(['code'])->get()->pluck('code', 'code')->toArray();
        $chargebackCodeList = DirectoryChargebackCode::select(['code'])->where('cc_type', $orderResult->card->cc_type)->get()->pluck('code', 'code')->toArray();

        $this->width(8, 4);
        $this->column(6, function (Form $form) use ($orderResult, $paymentOrderResult, $chargebackResult, $chargebackHistoryResult) {
            $form->text('order_id', '订单号')->disable()->default($orderResult->order_id)->required();
            $form->text('parent_order_id', '原始交易订单号')->disable()->default($orderResult->parent_order_id);
            $form->text('order_number', '原始商户订单号')->disable()->default($orderResult->order_number);
            $form->text('payment_order_id', '原始渠道交易订单号')->disable()->default($paymentOrderResult->payment_order_id);
            $form->text('merchant_id', 'MID')->disable()->default($orderResult->merchant_id);
            $form->text('merchant_name', '商户名')->disable()->default($orderResult->merchant_name);
            $form->text('channel', '账单标识')->disable()->default($orderResult->channel);
            $form->text('url_name', '交易网站')->disable()->default($orderResult->url_name);
            $form->text('card_number', '卡号')->disable()->default($orderResult->card->card_mask);
            $form->text('cc_type', '卡种')->disable()->default($orderResult->card->cc_type);
            $form->text('payment_order_currency', '原始授权币种')->disable()->default($paymentOrderResult->currency);
            $form->text('payment_order_amount', '原始授权金额')->disable()->default($paymentOrderResult->amount);
        });
        $this->column(6, function (Form $form) use ($orderResult, $paymentOrderResult, $chargebackResult, $chargebackHistoryResult, $currencyList, $chargebackCodeList) {
            $form->text('currency', '原始订单币种')->disable()->default($orderResult->currency);
            $form->text('amount', '原始订单金额')->disable()->default($orderResult->amount);
            $form->select('chargeback_currency', '拒付货币')->options($currencyList)->default($chargebackHistoryResult->currency)->required();
            $form->text('chargeback_amount', '拒付金额')->default($chargebackHistoryResult->amount)->type('float')->required();
            $form->select('chargeback_status', '拒付状态')->options(ChargebackHistory::$statusMap)->default($chargebackResult->chargeback_status)->disable();
            $form->select('chargeback_code', '拒付原因码')->options($chargebackCodeList)->default($chargebackHistoryResult->chargeback_code)->required();
            $form->datetime('chargeback_at', '拒付通知日期')->format('YYYY-MM-DD HH:mm:ss')->default($chargebackHistoryResult->chargeback_at)->required();
            $form->datetime('chargeback_deadline', '最晚响应日期')->format('YYYY-MM-DD HH:mm:ss')->default($chargebackResult->chargeback_deadline)->required();
            $form->text('arn', 'ARN')->default($chargebackHistoryResult->arn)->placeholder('请输入ARN')->required();
            $form->textarea('chargeback_remarks', '备注')->default($chargebackResult->remarks);
        });

        Admin::style(
            <<<CSS
            .row {
                margin-left: 0px;
                margin-right: 0px;
            }
CSS
        );

		// 引入iframe-tab后，找外层元素的方法要变更
		Admin::script(
			<<<JS
            document.body.style.cssText = "margin-bottom: 29px; height: auto;";
            $('body', window.parent.document)[0].style.cssText = "margin-bottom: 29px; height: auto;";
            $('body', window.parent.document).find("nav")[0].style.cssText = "top: 0;";

            // 确保ARN输入框可以正常输入
            $(document).ready(function() {
                setTimeout(function() {
                    var arnInput = $('input[name="arn"]');
                    if (arnInput.length > 0) {
                        arnInput.prop('disabled', false);
                        arnInput.prop('readonly', false);
                        arnInput.removeAttr('disabled');
                        arnInput.removeAttr('readonly');
                        console.log('ARN input field enabled');
                    }
                }, 500);
            });
JS
		);
    }

    protected function afterChargebackSettle(OrderSettlement $orderSettlement)
    {
        return event(new ChargebackSettle($orderSettlement));
    }
}
