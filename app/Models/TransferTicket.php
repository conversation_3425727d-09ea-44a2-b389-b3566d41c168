<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class TransferTicket extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'transfer_tickets';

    const TRANSFER_STATUS_FAILED = 0;
    const TRANSFER_STATUS_SUCCESS = 1;
    const TRANSFER_STATUS_CHECK = 2;
    const TRANSFER_STATUS_HANDLE = 3;
    const TRANSFER_STATUS_PROCESS = 4;
    const TRANSFER_STATUS_CANCEL = 5;
    const TRANSFER_STATUS_REJECT = 6;

    public static $transferStatusMap = [
        self::TRANSFER_STATUS_FAILED  => '提现失败',
        self::TRANSFER_STATUS_SUCCESS => '提现成功',
        self::TRANSFER_STATUS_CHECK   => '提现待审',
        self::TRANSFER_STATUS_HANDLE  => '提现操作',
        self::TRANSFER_STATUS_PROCESS => '提现处理',
        self::TRANSFER_STATUS_CANCEL  => '提现取消',
        self::TRANSFER_STATUS_REJECT  => '提现驳回'
    ];

    const INNER_BUCKLE    = 0;
    const EXTERNAL_BUCKLE = 1;

    public static $deductionMap = [
        self::INNER_BUCKLE    => '内扣',
        self::EXTERNAL_BUCKLE => '外扣',
    ];

    const TYPE_TRADER  = 0;
    const TYPE_BILLING = 1;

    public static $transferAccountTypeMap = [
        self::TYPE_TRADER  => '商户提现',
        self::TYPE_BILLING => '账单提现',
    ];

    public function accounts(){
        return $this->belongsToMany(TransferAccount::class, 'transfer_account_relation', 'ticket_id', 'transfer_account_id')->withPivot('note');
    }
}
