<?php

namespace App\Classes\Pay\Gateways\Acapay;

use App\Classes\Pay\Events;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Gateways\Acapay;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Log;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Acapay';
        $this->baseUri     = Acapay::URL[$config->get('mode', Acapay::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $post
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $post, string $requestId = '' ): Collection
    {
        $logData = Support::handleLogData($post);

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData], 
            $requestId
        ));

        try {
            $result = self::$instance->post(self::$instance->getBaseUri() . $endpoint, [], ['json' => $post['data'], 'headers' => $post['header']]);
        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                $e->getMessage(),
                [], 
                $requestId,
                'error'
            ));
        };

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 签名
     *
     * @param $data
     * @param $privateKey
     * @return string
     */
    public static function sign(&$data, $privateKey)
    {
        return self::genSign(self::genSignContent($data), $privateKey);
    }


    /**
     * 签名内容处理
     *
     * @param [array] $data
     * @return void
     */
    private static function genSignContent(&$data)
    {
        ksort($data);
        if (isset($data['param'])) {
            self::handleParam($data['param']);
        }

        $content = '';
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $content .= $key . '=' . json_encode($value, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '&';
            } else {
                $content .= $key . '=' . $value . '&';
            }
        }
        return rtrim($content, "&");
    }

    /**
     * 处理空数据
     * @param [type] &$param
     * @return void
     */
    private static function handleParam(&$param): void
    {
        if (!is_null($param) && is_array($param)) {
            foreach ($param as $pKey => $pValue) {
                if (is_null($pValue)) {
                    unset($param[$pKey]);
                }
            }
        }
    }

    /**
     * 生成签名
     *
     * @param $toSign
     * @param $privateKey
     * @return string
     */
    public static function genSign($toSign, $privateKey)
    {
        //这里他是拼接成和pem文件一样的格式
        $privateKey       = "-----BEGIN RSA PRIVATE KEY-----\n" .
            wordwrap($privateKey, 64, "\n", true) .
            "\n-----END RSA PRIVATE KEY-----";
        $binary_signature = "";
        $algo             = "SHA256";
        openssl_sign($toSign, $binary_signature, $privateKey, $algo);
        $sign = base64_encode($binary_signature);
        return $sign;
    }
    
    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        $card = $data['data']['param'] ?? [];

        if (isset($card['expiry_month'], $card['expiry_year'], $card['cvv'], $card['card_number'])) {
            $card                 = &$data['data']['param'];
            $card['expiry_month'] = get_mark_data($card['expiry_month']);
            $card['expiry_year']  = get_mark_data($card['expiry_year']);
            $card['cvv']          = get_mark_data($card['cvv']);
            $card['card_number']  = get_markcard($card['card_number']); 
        }
        
        return $data;
    }
}
